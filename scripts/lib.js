const fs = require('fs');
const { ChartJSNodeCanvas } = require('chartjs-node-canvas');

const standardPlotWidth = 1600;
const standardPlotHeight = 1200;

const defaultFontSize = 40;
const titleFontSize = defaultFontSize;
const legendTitleFontSize = defaultFontSize
const legendLabelFontSize = 36;
const dataLabelFontSize = defaultFontSize

// 10 colors
const colorScheme10 = [
    '#035c81', // deep blue
    '#34a270', // emerald
    '#8c4e9f', // orchid
    '#0c8dc3', // cerulean sky
    '#bcbebc', // cloud gray shade 15%
    '#e9a64c', // gold
    '#cf2fb3', // magenta
]

// 20 colors
const colorScheme20 = [
    '#035c81', // deep blue
    '#34a270', // emerald
    '#8c4e9f', // orchid
    '#0c8dc3', // cerulean sky
    '#e3e4e3', // cloud gray
    '#e9a64c', // gold
    '#cf2fb3', // magenta
]

const getColorScheme = arr => {
    if (arr.length <= 10) {
        return colorScheme10;
    }
    return colorScheme20;
}

const getCanvas = (type, width, height) => {
    return new ChartJSNodeCanvas({
        type: type,
        width: width,
        height: height,
        backgroundColour: 'white',
        plugins: {
            modern: ['chartjs-plugin-datalabels']
        }
    }); 
}

const renderPng = async(width, height, configuration, filename) => {
    console.log('PNG - starting');
    const canvas = getCanvas('png', width, height);
    const imageBuffer = await canvas.renderToBuffer(configuration);
    fs.writeFileSync(filename, imageBuffer);
    console.log("PNG - created successfully");
}

const renderSvg = async(width, height, configuration, filename) => {
    console.log('SVG - starting');
    const canvas = getCanvas('svg', width, height);
    const imageBuffer = await canvas.renderToBufferSync(configuration, "image/svg+xml")
    fs.writeFileSync(filename, imageBuffer);
    console.log("SVG - created successfully");
}

const renderDoughnutPlot = async(plotConfig) => {
    const colorScheme = getColorScheme(plotConfig.data);
    const configuration = {
        type: 'doughnut',
        data: {
            labels: plotConfig.labels,
            datasets: [
                {
                    label: plotConfig.datasetLabel,
                    data: plotConfig.data,
                    backgroundColor: colorScheme,
                    borderColor: colorScheme
                },
            ],
        },
        options: {
            plugins: {
                title: {
                    display: true,
                    text: plotConfig.tableName,
                    font: {
                        size: titleFontSize
                    }
                },
                legend: {
                    display: true,
                    position: 'right',
                    align: 'center',
                    title: {
                        display: true,
                        text: "Legend",
                        font: {
                            size: legendTitleFontSize
                        }
                    },
                    labels: {
                        font: {
                            size: legendLabelFontSize
                        }
                    }
                },
                datalabels: {
                    display: 'auto',
                    color: '#ffffff',
                    font: {
                        weight: 'bold',
                        size: dataLabelFontSize
                    }
                }
            }
        }
    };
    
    renderPng(standardPlotWidth, standardPlotHeight, configuration, plotConfig.filePrefix + ".png")
    renderSvg(standardPlotWidth, standardPlotHeight, configuration, plotConfig.filePrefix + ".svg")
    setTimeout(() => console.log('Exiting.'), 5000)
}


const renderBarPlot = async(plotConfig) => {
    const colorScheme = getColorScheme(plotConfig.data)
    const configuration = {
        type: 'bar',
        data: {
            labels: plotConfig.labels,
            datasets: [
                {
                    label: plotConfig.datasetLabel,
                    data: plotConfig.data,
                    backgroundColor: colorScheme,
                    borderColor: colorScheme,
                    borderWidth: 1
                }
            ],
        },
        options: {
            scales: {
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        font: {
                            size: dataLabelFontSize
                        }
                    }
                },
                y: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        font: {
                            size: dataLabelFontSize
                        }
                    },
                    beginAtZero: true,
                }
            },
            plugins: {
                title: {
                    display: true,
                    text: plotConfig.tableName,
                    font: {
                        size: titleFontSize
                    }
                },
                legend: {
                    display: false
                },
                datalabels: {
                    display: 'auto',
                    color: '#ffffff',
                    font: {
                        weight: 'bold',
                        size: dataLabelFontSize
                    }
                }
            }
        }
    };
    
    renderPng(standardPlotWidth, standardPlotHeight, configuration, plotConfig.filePrefix + ".png")
    renderSvg(standardPlotWidth, standardPlotHeight, configuration, plotConfig.filePrefix + ".svg")
    setTimeout(() => console.log('Exiting.'), 5000)
}

module.exports = {
    renderDoughnutPlot: renderDoughnutPlot,
    renderBarPlot: renderBarPlot,
};

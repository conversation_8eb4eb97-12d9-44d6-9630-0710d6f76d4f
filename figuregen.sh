# prior to running, ensure node v21.6.1 is running
# nvm use v21.6.1
# requires local installation of dnastack-client-library

DIRNAME="asap-crn-cloud-figures"
DATE_STAMP=$(date '+%Y-%m-%d')
OUTPUT_DIR="${HOME}/Downloads/${DIRNAME}"
INPUT_DATA_DIR="data"
GS_URI_START="gs://${DIRNAME}"
GS_URI_LATEST="${GS_URI_START}/latest"
GS_URI_SNAPSHOT="${GS_URI_START}/snapshot/${DATE_STAMP}"

rm -rf ${OUTPUT_DIR}
mkdir -p ${OUTPUT_DIR}

# PMDBS FIGURE GEN

echo "Generating PMDBS figures"

for TEAM in $(echo "cohort" "lee" "hafler" "jakobsson" "scherzer"); do
    echo "Generating PMDBS figure for ${TEAM}"

    node scripts/render-plot.js data/pmdbs/${TEAM}/sex.json ${OUTPUT_DIR}
    node scripts/render-plot.js data/pmdbs/${TEAM}/age-at-collection.json ${OUTPUT_DIR}
    node scripts/render-plot.js data/pmdbs/${TEAM}/pd-status.json ${OUTPUT_DIR}
    node scripts/render-plot.js data/pmdbs/${TEAM}/brain-region.json ${OUTPUT_DIR}
done

echo "Uploading figures"
gcloud storage cp -r ${OUTPUT_DIR}/* ${GS_URI_LATEST}
gcloud storage cp -r ${OUTPUT_DIR}/* ${GS_URI_SNAPSHOT}

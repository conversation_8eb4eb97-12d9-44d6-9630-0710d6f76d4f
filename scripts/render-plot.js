const lib = require('./lib.js');
const fs = require('fs');

// const prepLabelsAndData = () => {
//     console.log("reading in data");
//     const queryResult = JSON.parse(fs.readFileSync("data/nhs-fig4-00.json"));
//     const resultArr = queryResult.map(item => [item.country, item.country_count])
//     resultArr.sort((a, b) => b[1] - a[1])
//     const resultsFiltered = resultArr.filter(a => a[1] > 0)
//     const labels = resultsFiltered.map(e => e[0])
//     const data = resultsFiltered.map(e => e[1])
//     return [labels, data]
// }

// const outputDir = process.argv[2]
// const filePrefix = outputDir + "/natural-history-study-figure-4"
// var labelsAndData = prepLabelsAndData();
// 
// var plotConfig = {
//     labels: labelsAndData[0],
//     data: labelsAndData[1],
//     datasetLabel: "Nationality",
//     tableName: "Nationality",
//     filePrefix: filePrefix
// }

// lib.renderBarPlot(plotConfig);

const main = () => {
    const configFile = process.argv[2];
    const outputDir = process.argv[3];
    const config = JSON.parse(fs.readFileSync(configFile, 'utf8'))
    const plotConfig = {
        tableName: config.tableName,
        datasetLabel: config.tableName,
        filePrefix: outputDir + "/" + config.filePrefix,
        labels: Object.keys(config.data),
        data: Object.values(config.data)
    }

    if (config.plotType === "doughnut") {
        lib.renderDoughnutPlot(plotConfig);
    } else if (config.plotType === "bar") {
        lib.renderBarPlot(plotConfig);
    }
}

main()

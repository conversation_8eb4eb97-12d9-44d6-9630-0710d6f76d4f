# script for loading datasets 



# %% 
# #### generate a table defining the datasets for a release 
#  dataset_name, bucket_name, collection_name
#
#

# %%
import pandas as pd
from pathlib import Path
import os, sys
import json 
import subprocess

%load_ext autoreload
%autoreload 2

root_path = Path.cwd() 

# TODO:  make the paths more flexible

# datasets_path = root_path.parent / "asap-crn-cloud-dataset-metadata/datasets"
releases_path = root_path.parent / "asap-crn-cloud-release-resources/releases"

release_version = 'v2.0.1'

artifacts_path = releases_path / release_version / "release-artifacts"
# %% load dictionaries from jsons

with open( artifacts_path / "datasets.json", "r") as f:
    new_datasets = json.load(f)

with open( artifacts_path / "release_stats.json", "r") as f:
    new_stats = json.load(f)


# %%
for collection,dataset in new_datasets.items():
    print(f"\n{collection}")
    for ds in dataset:
        print(ds)   

## looks like we need to organize the minor releases... 
# maybe add a pmdbs layer to make dealing with mouse datasets easier


# %%

def make_age_at_collection_json(stats,ds_key):
    file_prefix = f"{ds_key}-age-at-collection"
    blob ={
        "plotType": "bar",
        "tableName": "Age at Collection",
        "datasetLabel": "Age at Collection",
        "filePrefix": file_prefix,
        "data": {
            "Min": float(stats["age"]["min"]),
            "Mean": float(stats["age"]["mean"]),
            "Median": float(stats["age"]["median"]),
            "Max": float(stats["age"]["max"])
        }
    }
    return blob




# %%
def make_sex_json(stats,ds_key):
    file_prefix = f"{ds_key}-sex"
    data = stats['sex'][0]
    blob = {
        "plotType": "doughnut",
        "tableName": "Sex",
        "datasetLabel": "Sex",
        "filePrefix": file_prefix,
        "data": data
    }
    return blob


# %%
def make_brain_region_json(stats,ds_key):
    file_prefix = f"{ds_key}-brain-region"
    # not sure why this dictionary is wrapped in a list
    data_keys = stats['brain_region'][0]
    data = {}
    for k,v in data_keys.items():
        data[k.title()] = v
    blob = {
        "plotType": "doughnut",
        "tableName": "Brain Region",
        "datasetLabel": "Brain Region",
        "filePrefix": file_prefix,
        "data": data
    }
    return blob

# %%

def make_pd_status_json(stats,ds_key):
    file_prefix = f"{ds_key}-pd-status"
    data = stats['PD_status'][0]
    blob = {
        "plotType": "doughnut",
        "tableName": "PD Status",
        "datasetLabel": "PD Status",
        "filePrefix": file_prefix,
        "data": data
    }

    return blob




# %%
def dump_stats_json(stats, f_path):
    if stats['tableName'] == "Age at Collection":
        print("age at collection")
        file_name = f_path / "age-at-collection.json"
    elif stats['tableName'] == "Sex":
        print("sex")
        file_name = f_path / "sex.json"
    elif stats['tableName'] == "Brain Region":
        print("brain region")
        file_name = f_path / "brain-region.json"
    elif stats['tableName'] == "PD Status":
        print("pd status")
        file_name = f_path / "pd-status.json"
    else:
        print("not implemented yet")
        file_name = f_path / stats['tableName'].replace(" ","-").lower() + ".json"
    with open(file_name, "w") as f:
        json.dump(stats,f,indent=4)

# %%
for collection, dataset in new_datasets.items():
    stats = new_stats[collection]    
    for ds,ds_stats in stats.items():
        ds_path = root_path / "datasets" / ds
        if not ds_path.exists():
            ds_path.mkdir(exist_ok=True)

        if "pmdbs" in ds:
            blob = make_age_at_collection_json(ds_stats,ds)
            dump_stats_json(blob,ds_path)
            blob = make_sex_json(ds_stats,ds)
            dump_stats_json(blob,ds_path)
            blob = make_brain_region_json(ds_stats,ds)
            dump_stats_json(blob,ds_path)
            blob = make_pd_status_json(ds_stats,ds)
            dump_stats_json(blob,ds_path)
        else:
            print("not pmdbs... no implimentation yet")
            pass




# %% export figures

datasets = root_path / "datasets"

node = "/Users/<USER>/.nvm/versions/node/v21.6.1/bin/node"

figure_dir = root_path / "figures" / release_version
if not figure_dir.exists():
    figure_dir.mkdir(exist_ok=True, parents=True)
    print(f"created {figure_dir}")

datasets_path = root_path / "datasets" 
for ds in datasets_path.iterdir():
    if ds.is_dir():
        print(ds)
        
        cmd = f"{node} {root_path/'scripts/render-plot.js'} {ds/'age-at-collection.json'} {figure_dir}"
        cmd = cmd.split(" ")
        result = subprocess.Popen(cmd, stdout=subprocess.PIPE)
        cmd = f"{node} {root_path/'scripts/render-plot.js'} {ds/'pd-status.json'} {figure_dir}"
        cmd = cmd.split(" ")
        result = subprocess.Popen(cmd, stdout=subprocess.PIPE)
        cmd = f"{node} {root_path/'scripts/render-plot.js'} {ds/'sex.json'} {figure_dir}"
        cmd = cmd.split(" ")
        result = subprocess.Popen(cmd, stdout=subprocess.PIPE)
        cmd = f"{node} {root_path/'scripts/render-plot.js'} {ds/'brain-region.json'} {figure_dir}"
        cmd = cmd.split(" ")
        result = subprocess.Popen(cmd, stdout=subprocess.PIPE)
        

# print(cmd)




# %%
# TODO: creat CLI
def main():
    pass

import argparse

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    args = parser.parse_args()
    print(args)
    
    main()

# script for loading datasets 



# %% 
# #### generate a table defining the datasets for a release 
#  dataset_name, bucket_name, collection_name
#
#

# %%
import pandas as pd
from pathlib import Path
import os, sys
import json 
import subprocess

%load_ext autoreload
%autoreload 2

root_path = Path.cwd() 

# TODO:  make the paths more flexible

# datasets_path = root_path.parent / "asap-crn-cloud-dataset-metadata/datasets"
releases_path = root_path.parent / "asap-crn-cloud-release-resources/releases"

release_version = 'v3.0.0'

artifacts_path = releases_path / release_version

stats_path = root_path / "datasets"

# %%
#
#. HELPERS. (TODO: move to separate module)
# 
# 


def make_age_at_collection_json(stats,ds_key):
    file_prefix = f"{ds_key}-age-at-collection"

    blob ={
        "plotType": "bar",
        "tableName": "Age (years)",
        "datasetLabel": "Age (years)",
        "filePrefix": file_prefix,
        "data": {
            "Min": float(stats["age"]["min"]),
            "Mean": float(stats["age"]["mean"]),
            "Median": float(stats["age"]["median"]),
            "Max": float(stats["age"]["max"])
        }
    }
    return blob



def make_age_at_collection_mouse_json(stats,ds_key):
    file_prefix = f"{ds_key}-age-at-collection"
    if stats["age"]['min'] == "NaN":
        # HACK: only one "age"
        print("only one age!!")
        data = {
            # "Min": float(stats["age"]["min"]),
            # "Mean": float(stats["age"]["mean"]),
            # "Median": float(stats["age"]["median"]),
            # "Max": float(stats["age"]["max"])
        }
        return data

    blob ={
        "plotType": "bar",
        "tableName": "Age (days)",
        "datasetLabel": "Age (days)",
        "filePrefix": file_prefix,
        "data": {
            "Min": float(stats["age"]["min"]),
            "Mean": float(stats["age"]["mean"]),
            "Median": float(stats["age"]["median"]),
            "Max": float(stats["age"]["max"])
        }
    }
    return blob




# %%
def make_sex_json(stats,ds_key):
    file_prefix = f"{ds_key}-sex"
    data = stats['sex'][0]
    blob = {
        "plotType": "doughnut",
        "tableName": "Sex",
        "datasetLabel": "Sex",
        "filePrefix": file_prefix,
        "data": data
    }
    return blob


# %%
def make_brain_region_json(stats,ds_key):
    file_prefix = f"{ds_key}-brain-region"
    # not sure why this dictionary is wrapped in a list
    data_keys = stats['brain_region'] #[0]
    data = {}
    for k,v in data_keys.items():
        data[k.title()] = v
    blob = {
        "plotType": "doughnut",
        "tableName": "Brain Region (samples)",
        "datasetLabel": "Brain Region (samples)",
        "filePrefix": file_prefix,
        "data": data
    }
    return blob

# %%

def make_pd_status_json(stats,ds_key):
    file_prefix = f"{ds_key}-pd-status"
    data = stats['PD_status'][0]
    blob = {
        "plotType": "doughnut",
        "tableName": "PD Status",
        "datasetLabel": "PD Status",
        "filePrefix": file_prefix,
        "data": data
    }

    return blob


def make_condition_id_json(stats,ds_key):
    file_prefix = f"{ds_key}-condition-id"
    data = stats['condition_id'][0]
    blob = {
        "plotType": "doughnut",
        "tableName": "Condition ID",
        "datasetLabel": "Condition ID",
        "filePrefix": file_prefix,
        "data": data
    }

    return blob



# %%
def dump_stats_json(stats, f_path):
    if stats['tableName'] == "Age (years)":
        print("age at collection (human)")
        file_name = f_path / "age-at-collection.json"
    elif stats['tableName'] == "Age (days)":
        print("age at collection (mouse)")
        file_name = f_path / "age-at-collection.json"
    elif stats['tableName'] == "Sex":
        print("sex")
        file_name = f_path / "sex.json"
    elif stats['tableName'] == "Brain Region (samples)":
        print("brain region")
        file_name = f_path / "brain-region.json"
    elif stats['tableName'] == "PD Status":
        print("pd status")
        file_name = f_path / "pd-status.json"

    elif stats['tableName'] == "Condition ID":
        print("pd status")
        file_name = f_path / "condition-id.json"

    else:
        print(f"not implemented yet {stats['tableName']}")
        file_name = f_path / (stats['tableName'].replace(" ","-").lower() + ".json")
        
    with open(file_name, "w") as f:
        json.dump(stats,f,indent=4)


# %% load dictionaries from jsons
new_datasets = pd.read_csv(artifacts_path / "new_datasets.csv") # all new

# %%
# prep all the datasets ...


for row, dataset in new_datasets.iterrows():

    collection = dataset['collection']
    dataset_name = dataset['dataset_name']
    dataset_version = dataset["dataset_version"]
    schema_version = dataset["cde_version"]
    latest_release = dataset["latest_release"]

    # type
    # source = 
    ds_source = dataset["dataset_type"]
    # cohort
    cohort = dataset["cohort"]

    # type
    if "pmdbs" in ds_source:
        source = "pmdbs"
    elif ("mouse" in ds_source):
        source = "mouse"
    elif ("cell" in ds_source):
        source = "cell"
    else:
        source = ds_source

    ds_path = artifacts_path / "datasets" / dataset_name
    out_path = stats_path / dataset_name
    if not out_path.exists():
        out_path.mkdir(exist_ok=True, parents=True)


    # load stats
    with open( ds_path / "release_stats.json", "r") as f:
        new_stats = json.load(f)

    if "pmdbs" in source:
        subject_stats = new_stats["subject"]
        sample_stats = new_stats["samples"]
        subject_stats["age"] = subject_stats.pop("age_at_collection")
        sample_stats["age"] = sample_stats.pop("age_at_collection")

        blob = make_age_at_collection_json(subject_stats,dataset_name)
        dump_stats_json(blob,out_path)
        
        blob = make_pd_status_json(subject_stats,dataset_name)
        dump_stats_json(blob,out_path)

        blob = make_sex_json(subject_stats,dataset_name)
        dump_stats_json(blob,out_path)

        blob = make_brain_region_json(sample_stats,dataset_name)
        dump_stats_json(blob,out_path)
        

    elif "mouse" in source:
        # subject_stats = new_stats["subject"]
        # sample_stats = new_stats["samples"]
        # subject_stats["age"] = subject_stats.pop("age_at_collection")
        # sample_stats["age"] = sample_stats.pop("age_at_collection")
        subject_stats = new_stats
        blob = make_age_at_collection_mouse_json(subject_stats,dataset_name)
        dump_stats_json(blob,out_path)
        
        blob = make_condition_id_json(subject_stats,dataset_name)
        dump_stats_json(blob,out_path)

        blob = make_sex_json(subject_stats,dataset_name)
        dump_stats_json(blob,out_path)

        
    else:
        print("not implemented yet")
        pass


# %% export figures

datasets = root_path / "datasets"

node = "/Users/<USER>/.nvm/versions/node/v21.6.1/bin/node"

figure_dir = root_path / "figures" / release_version
if not figure_dir.exists():
    figure_dir.mkdir(exist_ok=True, parents=True)
    print(f"created {figure_dir}")

datasets_path = root_path / "datasets" 
for ds in datasets_path.iterdir():
    if ds.is_dir():
        print(ds)
        
        cmd = f"{node} {root_path/'scripts/render-plot.js'} {ds/'age-at-collection.json'} {figure_dir}"
        cmd = cmd.split(" ")
        result = subprocess.Popen(cmd, stdout=subprocess.PIPE)
        cmd = f"{node} {root_path/'scripts/render-plot.js'} {ds/'pd-status.json'} {figure_dir}"
        cmd = cmd.split(" ")
        result = subprocess.Popen(cmd, stdout=subprocess.PIPE)
        cmd = f"{node} {root_path/'scripts/render-plot.js'} {ds/'sex.json'} {figure_dir}"
        cmd = cmd.split(" ")
        result = subprocess.Popen(cmd, stdout=subprocess.PIPE)
        cmd = f"{node} {root_path/'scripts/render-plot.js'} {ds/'brain-region.json'} {figure_dir}"
        cmd = cmd.split(" ")
        result = subprocess.Popen(cmd, stdout=subprocess.PIPE)
        

# print(cmd)













# %% export figures

datasets = root_path / "datasets"

node = "/Users/<USER>/.nvm/versions/node/v21.6.1/bin/node"

figure_dir = root_path / "figures" / release_version
if not figure_dir.exists():
    figure_dir.mkdir(exist_ok=True, parents=True)
    print(f"created {figure_dir}")

datasets_path = root_path / "datasets" 

for row, dataset in new_datasets.iterrows():
    print(dataset['dataset_name'])
    ds_path = datasets_path / dataset['dataset_name']
    source = dataset["dataset_type"]
    # AGE
    cmd = f"{node} {root_path/'scripts/render-plot.js'} {ds_path/'age-at-collection.json'} {figure_dir}"
    cmd = cmd.split(" ")
    result = subprocess.Popen(cmd, stdout=subprocess.PIPE)
    
    # GENDER
    cmd = f"{node} {root_path/'scripts/render-plot.js'} {ds_path/'sex.json'} {figure_dir}"
    cmd = cmd.split(" ")
    result = subprocess.Popen(cmd, stdout=subprocess.PIPE)

    
    if "pmdbs" in source:
        cmd = f"{node} {root_path/'scripts/render-plot.js'} {ds_path/'pd-status.json'} {figure_dir}"
        cmd = cmd.split(" ")
        result = subprocess.Popen(cmd, stdout=subprocess.PIPE)

        cmd = f"{node} {root_path/'scripts/render-plot.js'} {ds_path/'brain-region.json'} {figure_dir}"
        cmd = cmd.split(" ")
        result = subprocess.Popen(cmd, stdout=subprocess.PIPE)

    elif "mouse" in source:
        cmd = f"{node} {root_path/'scripts/render-plot.js'} {ds_path/'condition-id.json'} {figure_dir}"
        cmd = cmd.split(" ")
        result = subprocess.Popen(cmd, stdout=subprocess.PIPE)

    else:
        print("not implemented yet")
        pass

# print(cmd)




# %%
# TODO: creat CLI
def main():
    pass

import argparse

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    args = parser.parse_args()
    print(args)
    
    main()
